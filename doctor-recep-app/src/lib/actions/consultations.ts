'use server'

import { revalidatePath } from 'next/cache'
import { createClient } from '@/lib/supabase/server'
import { verifySession } from '@/lib/auth/dal'
import { ConsultationCreateSchema, ConsultationUpdateSchema } from '@/lib/validations'
import { ApiResponse, Consultation } from '@/lib/types'

export async function createConsultation(formData: FormData): Promise<ApiResponse<Consultation>> {
  try {
    const session = await verifySession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }

    // Parse images if provided
    const imagesBase64String = formData.get('images_base64') as string
    let imagesBase64: string[] = []
    if (imagesBase64String) {
      try {
        imagesBase64 = JSON.parse(imagesBase64String)
      } catch (error) {
        console.error('Error parsing images_base64:', error)
      }
    }

    // Validate form data
    const validatedFields = ConsultationCreateSchema.safeParse({
      audio_base64: formData.get('audio_base64'),
      images_base64: imagesBase64.length > 0 ? imagesBase64 : undefined,
      submitted_by: formData.get('submitted_by'),
    })

    if (!validatedFields.success) {
      return {
        success: false,
        error: 'Invalid form data',
        data: validatedFields.error.flatten().fieldErrors
      }
    }

    const { audio_base64, images_base64, submitted_by } = validatedFields.data

    const supabase = await createClient()

    // Insert consultation
    const { data: consultation, error } = await supabase
      .from('consultations')
      .insert({
        doctor_id: session.userId,
        audio_base64,
        images_base64: images_base64 || [],
        submitted_by,
        status: 'pending'
      })
      .select()
      .single()

    if (error) {
      console.error('Database error:', error)
      return { success: false, error: 'Failed to create consultation' }
    }

    revalidatePath('/dashboard')
    revalidatePath('/mobile')

    return { success: true, data: consultation as Consultation }
  } catch (error) {
    console.error('Create consultation error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function generateSummary(consultationId: string, additionalImages?: string[]): Promise<ApiResponse<string>> {
  try {
    const session = await verifySession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }

    const supabase = await createClient()

    // Check quota before proceeding
    const { data: quotaCheck } = await supabase
      .rpc('check_and_update_quota', { doctor_uuid: session.userId })

    if (!quotaCheck) {
      return {
        success: false,
        error: 'Quota exceeded. You have reached your monthly AI generation limit. Please contact admin or wait for next month.'
      }
    }

    // Get consultation and doctor data
    const { data: consultation, error: consultationError } = await supabase
      .from('consultations')
      .select(`
        *,
        doctors (template_config)
      `)
      .eq('id', consultationId)
      .eq('doctor_id', session.userId)
      .single()

    if (consultationError || !consultation) {
      return { success: false, error: 'Consultation not found' }
    }

    // Combine original images with additional images
    const allImages = [
      ...(consultation.images_base64 || []),
      ...(additionalImages || [])
    ]

    // Call Gemini API
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        audio_base64: consultation.audio_base64,
        images_base64: allImages,
        template_config: consultation.doctors.template_config,
        submitted_by: consultation.submitted_by,
      }),
    })

    if (!response.ok) {
      return { success: false, error: 'Failed to generate summary' }
    }

    const { generated_summary } = await response.json()

    // Update consultation with generated summary
    const { error: updateError } = await supabase
      .from('consultations')
      .update({
        ai_generated_note: generated_summary,
        status: 'generated'
      })
      .eq('id', consultationId)

    if (updateError) {
      console.error('Update error:', updateError)
      return { success: false, error: 'Failed to save generated summary' }
    }

    revalidatePath('/dashboard')

    return { success: true, data: generated_summary }
  } catch (error) {
    console.error('Generate summary error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function approveConsultation(
  consultationId: string,
  editedNote: string
): Promise<ApiResponse<boolean>> {
  try {
    const session = await verifySession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }

    // Validate edited note
    const validatedFields = ConsultationUpdateSchema.safeParse({
      edited_note: editedNote,
    })

    if (!validatedFields.success) {
      return { success: false, error: 'Invalid note content' }
    }

    const supabase = await createClient()

    // Update consultation
    const { error } = await supabase
      .from('consultations')
      .update({
        edited_note: editedNote,
        status: 'approved'
      })
      .eq('id', consultationId)
      .eq('doctor_id', session.userId)

    if (error) {
      console.error('Update error:', error)
      return { success: false, error: 'Failed to approve consultation' }
    }

    revalidatePath('/dashboard')

    return { success: true, data: true }
  } catch (error) {
    console.error('Approve consultation error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function getConsultations(status?: string): Promise<ApiResponse<Consultation[]>> {
  try {
    const session = await verifySession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }

    const supabase = await createClient()

    let query = supabase
      .from('consultations')
      .select('*')
      .eq('doctor_id', session.userId)
      .order('created_at', { ascending: false })

    if (status) {
      query = query.eq('status', status)
    }

    const { data: consultations, error } = await query

    if (error) {
      console.error('Database error:', error)
      return { success: false, error: 'Failed to fetch consultations' }
    }

    return { success: true, data: consultations as Consultation[] }
  } catch (error) {
    console.error('Get consultations error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}
