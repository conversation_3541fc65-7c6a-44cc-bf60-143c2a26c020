// TypeScript types for the Doctor Voice & Image Summary System

export interface Doctor {
  id: string;
  email: string;
  name: string;
  phone?: string;
  clinic_name?: string;
  template_config: TemplateConfig;
  // Quota system fields
  monthly_quota: number;
  quota_used: number;
  quota_reset_at: string;
  // Admin approval system
  approved: boolean;
  approved_by?: string;
  approved_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Admin {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'super_admin';
  created_at: string;
  updated_at: string;
}

export interface UsageLog {
  id: string;
  doctor_id: string;
  consultation_id?: string;
  action_type: 'ai_generation' | 'quota_reset' | 'quota_update';
  quota_before?: number;
  quota_after?: number;
  metadata: Record<string, any>;
  created_at: string;
}

export interface TemplateConfig {
  prescription_format: 'standard' | 'detailed' | 'minimal';
  language: 'english' | 'hindi' | 'tamil' | 'telugu' | 'bengali';
  tone: 'professional' | 'friendly' | 'formal';
  sections: string[];
}

export interface Consultation {
  id: string;
  doctor_id: string;
  submitted_by: 'doctor' | 'receptionist';
  audio_base64: string;
  additional_audio?: string[]; // Array of additional audio recordings
  images_base64?: string[]; // Updated to match database schema
  ai_generated_note?: string;
  edited_note?: string;
  status: 'pending' | 'generated' | 'approved';
  patient_number?: number;
  created_at: string;
  updated_at: string;
}

export interface SessionPayload {
  userId: string;
  expiresAt: Date;
}

export interface FormState {
  errors?: {
    name?: string[];
    email?: string[];
    password?: string[];
    clinic_name?: string[];
  };
  message?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ConsultationCreateRequest {
  audio_base64: string;
  images_base64?: string[];
  submitted_by: 'doctor' | 'receptionist';
}

export interface ConsultationGenerateRequest {
  consultation_id: string;
}

export interface ConsultationApproveRequest {
  consultation_id: string;
  edited_note: string;
}

export interface GeminiApiRequest {
  audio_base64: string;
  images_base64?: string[];
  template_config: TemplateConfig;
  submitted_by: 'doctor' | 'receptionist';
}

export interface GeminiApiResponse {
  generated_summary: string;
  confidence_score?: number;
}

// Audio recording types
export interface AudioRecordingState {
  isRecording: boolean;
  audioBlob?: Blob;
  audioBase64?: string;
  duration: number;
  error?: string;
}

// Image capture types
export interface ImageCaptureState {
  images: ImageFile[];
  error?: string;
}

export interface ImageFile {
  id: string;
  blob: Blob;
  base64: string;
  preview: string;
  name: string;
}

// Dashboard types
export interface DashboardStats {
  total_consultations: number;
  pending_consultations: number;
  generated_consultations: number;
  approved_consultations: number;
  today_consultations: number;
}

// Admin dashboard types
export interface AdminDashboardStats {
  total_doctors: number;
  pending_approvals: number;
  approved_doctors: number;
  total_consultations: number;
  total_ai_generations: number;
  quota_usage_percentage: number;
}

export interface DoctorWithStats extends Doctor {
  total_consultations: number;
  this_month_generations: number;
  quota_percentage: number;
  last_activity?: string;
}

// Quota types
export interface QuotaInfo {
  monthly_quota: number;
  quota_used: number;
  quota_remaining: number;
  quota_percentage: number;
  quota_reset_at: string;
  days_until_reset: number;
}

// Admin action types
export interface AdminActionRequest {
  action: 'approve' | 'reject' | 'update_quota' | 'disable' | 'enable';
  doctor_id: string;
  data?: {
    quota?: number;
    reason?: string;
  };
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// Retry configuration
export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
}

// WhatsApp integration types (for future use)
export interface WhatsAppMessage {
  to: string;
  message: string;
  consultation_id: string;
}
