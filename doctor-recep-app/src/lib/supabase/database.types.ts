// Auto-generated types for Supabase database schema
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      doctors: {
        Row: {
          id: string
          email: string
          password_hash: string
          name: string
          phone: string | null
          clinic_name: string | null
          template_config: Json
          monthly_quota: number
          quota_used: number
          quota_reset_at: string
          approved: boolean
          approved_by: string | null
          approved_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          password_hash: string
          name: string
          phone?: string | null
          clinic_name?: string | null
          template_config?: Json
          monthly_quota?: number
          quota_used?: number
          quota_reset_at?: string
          approved?: boolean
          approved_by?: string | null
          approved_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          password_hash?: string
          name?: string
          phone?: string | null
          clinic_name?: string | null
          template_config?: Json
          monthly_quota?: number
          quota_used?: number
          quota_reset_at?: string
          approved?: boolean
          approved_by?: string | null
          approved_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      admins: {
        Row: {
          id: string
          email: string
          password_hash: string
          name: string
          role: 'admin' | 'super_admin'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          password_hash: string
          name: string
          role?: 'admin' | 'super_admin'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          password_hash?: string
          name?: string
          role?: 'admin' | 'super_admin'
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      usage_logs: {
        Row: {
          id: string
          doctor_id: string
          consultation_id: string | null
          action_type: 'ai_generation' | 'quota_reset' | 'quota_update'
          quota_before: number | null
          quota_after: number | null
          metadata: Json
          created_at: string
        }
        Insert: {
          id?: string
          doctor_id: string
          consultation_id?: string | null
          action_type: 'ai_generation' | 'quota_reset' | 'quota_update'
          quota_before?: number | null
          quota_after?: number | null
          metadata?: Json
          created_at?: string
        }
        Update: {
          id?: string
          doctor_id?: string
          consultation_id?: string | null
          action_type?: 'ai_generation' | 'quota_reset' | 'quota_update'
          quota_before?: number | null
          quota_after?: number | null
          metadata?: Json
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "usage_logs_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "doctors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "usage_logs_consultation_id_fkey"
            columns: ["consultation_id"]
            isOneToOne: false
            referencedRelation: "consultations"
            referencedColumns: ["id"]
          }
        ]
      }
      consultations: {
        Row: {
          id: string
          doctor_id: string
          submitted_by: 'doctor' | 'receptionist'
          audio_base64: string
          images_base64: Json | null
          ai_generated_note: string | null
          edited_note: string | null
          status: 'pending' | 'generated' | 'approved'
          patient_number: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          doctor_id: string
          submitted_by: 'doctor' | 'receptionist'
          audio_base64: string
          images_base64?: Json | null
          ai_generated_note?: string | null
          edited_note?: string | null
          status?: 'pending' | 'generated' | 'approved'
          patient_number?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          doctor_id?: string
          submitted_by?: 'doctor' | 'receptionist'
          audio_base64?: string
          images_base64?: Json | null
          ai_generated_note?: string | null
          edited_note?: string | null
          status?: 'pending' | 'generated' | 'approved'
          patient_number?: number | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "consultations_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "doctors"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
