{"name": "doctor-recep-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "create-admin": "node scripts/create-admin.js", "test-quota": "node tests/quota-system-test.js"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "dotenv": "^16.5.0", "jose": "^6.0.11", "lucide-react": "^0.511.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "readline": "^1.3.0", "tailwind-merge": "^3.3.0", "zod": "^3.25.28"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}