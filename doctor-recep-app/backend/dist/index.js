"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const compression_1 = __importDefault(require("compression"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const dotenv_1 = __importDefault(require("dotenv"));
const generative_ai_1 = require("@google/generative-ai");
dotenv_1.default.config();
const app = (0, express_1.default)();
const port = process.env.PORT || 3001;
// Initialize Gemini AI
const genAI = new generative_ai_1.GoogleGenerativeAI(process.env.GEMINI_API_KEY);
// Middleware
app.use((0, helmet_1.default)());
app.use((0, compression_1.default)());
app.use((0, cors_1.default)({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true
}));
// Rate limiting
const limiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);
// Body parser with larger limits for base64 data
app.use(express_1.default.json({ limit: '50mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '50mb' }));
// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});
// Generate summary endpoint
app.post('/api/generate-summary', async (req, res) => {
    try {
        const { audio_base64, images_base64, template_config, submitted_by } = req.body;
        if (!audio_base64) {
            return res.status(400).json({ error: 'Audio recording is required' });
        }
        // Generate prompt based on template config
        const prompt = generatePrompt(template_config, submitted_by);
        // Prepare content parts for Gemini
        const contentParts = [prompt];
        // Add audio part
        contentParts.push({
            inlineData: {
                mimeType: "audio/webm",
                data: audio_base64
            }
        });
        // Add image parts if provided
        if (images_base64 && Array.isArray(images_base64)) {
            images_base64.forEach((imageBase64) => {
                contentParts.push({
                    inlineData: {
                        mimeType: "image/jpeg",
                        data: imageBase64
                    }
                });
            });
        }
        // Get Gemini model
        const model = genAI.getGenerativeModel({ model: "gemini-2.5-flash-preview-05-20" });
        // Generate content
        const result = await model.generateContent(contentParts);
        const response = await result.response;
        const generated_summary = response.text();
        res.json({
            generated_summary,
            confidence_score: 0.95, // Placeholder - Gemini doesn't provide confidence scores
            processing_time: Date.now()
        });
    }
    catch (error) {
        console.error('Error generating summary:', error);
        res.status(500).json({
            error: 'Failed to generate summary',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});
// Generate prompt function
function generatePrompt(templateConfig, submittedBy) {
    // Provide default values if templateConfig is empty or missing properties
    const { prescription_format = 'structured', language = 'English', tone = 'professional', sections = ['Chief Complaint', 'History', 'Examination', 'Diagnosis', 'Treatment Plan', 'Follow-up'] } = templateConfig || {};
    const contextNote = submittedBy === 'doctor'
        ? 'This consultation was recorded by the doctor during patient visit.'
        : 'This consultation is being reviewed by the receptionist for final summary.';
    return `
You are an AI assistant helping Indian doctors create concise patient consultation summaries.

Context: ${contextNote}

IMPORTANT: Only include information that was actually mentioned by the doctor in the audio. Do not add assumptions, differential diagnoses, or recommendations not explicitly stated.

Requirements:
- Language: ${language}
- Tone: ${tone}
- Format: ${prescription_format}
- Include sections: ${Array.isArray(sections) ? sections.join(', ') : sections}

Instructions:
1. Transcribe only what the doctor actually said
2. Extract key medical information mentioned in the audio
3. If images provided, include any relevant handwritten notes or prescriptions
4. Keep the summary concise and factual
5. Use appropriate medical terminology for Indian healthcare context
6. Only include medications, dosages, and advice explicitly mentioned by the doctor
7. Do not add "further investigation needed" or similar phrases unless the doctor said so
8. If information is missing from audio, simply omit that section rather than noting it's missing

Please provide a concise, factual patient consultation summary based only on what was actually said in the audio recording.
  `.trim();
}
// Error handling middleware
app.use((error, req, res, next) => {
    console.error('Unhandled error:', error);
    res.status(500).json({
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
});
// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({ error: 'Endpoint not found' });
});
app.listen(port, () => {
    console.log(`🚀 Doctor Reception API running on port ${port}`);
    console.log(`📊 Health check: http://localhost:${port}/health`);
});
exports.default = app;
//# sourceMappingURL=index.js.map